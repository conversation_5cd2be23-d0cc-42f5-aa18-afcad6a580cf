package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import fi.dy.masa.litematica.data.DataManager;
import fi.dy.masa.litematica.schematic.placement.SchematicPlacement;
import fi.dy.masa.litematica.schematic.placement.SchematicPlacementManager;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;

// Litematica imports will be handled via reflection to avoid dependency issues

public class LitematicaMover extends Module {
    private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
    private final SettingGroup sgMovement = this.settings.createGroup("Movement");

    // Settings
    private final Setting<Direction> direction = sgMovement.add(new EnumSetting.Builder<Direction>()
        .name("direction")
        .description("Direction to move the schematic.")
        .defaultValue(Direction.NORTH)
        .build()
    );

    private final Setting<Integer> speed = sgMovement.add(new IntSetting.Builder()
        .name("speed")
        .description("Movement speed in ticks (20 ticks = 1 second).")
        .defaultValue(20)
        .min(1)
        .max(200)
        .sliderMax(100)
        .build()
    );

    private final Setting<Boolean> autoDetect = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-detect")
        .description("Automatically detect and move the currently selected schematic.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> logMovement = sgGeneral.add(new BoolSetting.Builder()
        .name("log-movement")
        .description("Log movement actions to chat.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> followPlayer = sgGeneral.add(new BoolSetting.Builder()
        .name("follow-player")
        .description("Make the schematic follow the player's horizontal movement.")
        .defaultValue(false)
        .build()
    );

    // Internal variables
    private int tickCounter = 0;
    private SchematicPlacement currentPlacement = null;

    // Follow player variables
    private BlockPos lastPlayerPos = null;
    private BlockPos relativeOffset = null;
    private boolean isFollowingInitialized = false;

    public LitematicaMover() {
        super(AddonTemplate.MyCategory, "投影跟随", "Automatically moves the currently selected Litematica schematic every second.");
    }

    @Override
    public void onActivate() {
        tickCounter = 0;
        isFollowingInitialized = false;
        lastPlayerPos = null;
        relativeOffset = null;

        if (logMovement.get()) {
            info("Litematica Mover activated");
        }

        // Try to get current placement when activated
        getCurrentPlacement();

        // Initialize follow player if enabled
        if (followPlayer.get()) {
            initializeFollowPlayer();
        }
    }

    @Override
    public void onDeactivate() {
        if (logMovement.get()) {
            info("Litematica Mover deactivated");
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (MinecraftClient.getInstance().player == null || MinecraftClient.getInstance().world == null) {
            return;
        }

        // Handle follow player logic
        if (followPlayer.get()) {
            handleFollowPlayer();
        } else {
            // Normal movement logic
            tickCounter++;

            // Move every 'speed' ticks
            if (tickCounter >= speed.get()) {
                tickCounter = 0;
                moveSchematic();
            }
        }
    }

    private SchematicPlacement getCurrentPlacement() {
        if (!autoDetect.get()) {
            return currentPlacement;
        }

        try {
            SchematicPlacementManager placementManager = DataManager.getSchematicPlacementManager();
            if (placementManager != null) {
                currentPlacement = placementManager.getSelectedSchematicPlacement();
            }
        } catch (Exception e) {
            if (logMovement.get()) {
                error("Failed to get current schematic placement: " + e.getMessage());
            }
        }

        return currentPlacement;
    }

    private void moveSchematic() {
        SchematicPlacement placement = getCurrentPlacement();

        if (placement == null) {
            if (logMovement.get()) {
                warning("No schematic placement selected");
            }
            return;
        }

        try {
            // Get current origin position
            BlockPos currentOrigin = placement.getOrigin();

            // Calculate new position based on direction
            BlockPos newOrigin = currentOrigin.offset(direction.get());

            // Set new origin position
            placement.setOrigin(newOrigin, null);

            if (logMovement.get()) {
                info("Moved schematic from " + currentOrigin.toShortString() + " to " + newOrigin.toShortString());
            }

        } catch (Exception e) {
            if (logMovement.get()) {
                error("Failed to move schematic: " + e.getMessage());
            }
        }
    }

    // Utility methods for different movement directions
    public void moveNorth() {
        direction.set(Direction.NORTH);
    }

    public void moveSouth() {
        direction.set(Direction.SOUTH);
    }

    public void moveEast() {
        direction.set(Direction.EAST);
    }

    public void moveWest() {
        direction.set(Direction.WEST);
    }

    public void moveUp() {
        direction.set(Direction.UP);
    }

    public void moveDown() {
        direction.set(Direction.DOWN);
    }

    // Getters for external access
    public Direction getCurrentDirection() {
        return direction.get();
    }

    public int getCurrentSpeed() {
        return speed.get();
    }

    public boolean isAutoDetectEnabled() {
        return autoDetect.get();
    }

    // Follow player methods
    private void initializeFollowPlayer() {
        if (MinecraftClient.getInstance().player == null) {
            return;
        }

        SchematicPlacement placement = getCurrentPlacement();
        if (placement == null) {
            if (logMovement.get()) {
                warning("No schematic placement found for follow player initialization");
            }
            return;
        }

        try {
            BlockPos playerPos = MinecraftClient.getInstance().player.getBlockPos();
            BlockPos schematicPos = placement.getOrigin();

            // Calculate relative offset (only X and Z, ignore Y)
            relativeOffset = new BlockPos(
                schematicPos.getX() - playerPos.getX(),
                0, // Don't track Y offset
                schematicPos.getZ() - playerPos.getZ()
            );

            lastPlayerPos = new BlockPos(playerPos.getX(), 0, playerPos.getZ());
            isFollowingInitialized = true;

            if (logMovement.get()) {
                info("Follow player initialized. Relative offset: " + relativeOffset.getX() + ", " + relativeOffset.getZ());
            }
        } catch (Exception e) {
            if (logMovement.get()) {
                error("Failed to initialize follow player: " + e.getMessage());
            }
        }
    }

    private void handleFollowPlayer() {
        if (MinecraftClient.getInstance().player == null) {
            return;
        }

        // Initialize if not done yet
        if (!isFollowingInitialized) {
            initializeFollowPlayer();
            return;
        }

        try {
            BlockPos currentPlayerPos = MinecraftClient.getInstance().player.getBlockPos();
            BlockPos currentPlayerPosFlat = new BlockPos(currentPlayerPos.getX(), 0, currentPlayerPos.getZ());

            // Check if player moved horizontally
            if (lastPlayerPos == null || !lastPlayerPos.equals(currentPlayerPosFlat)) {
                moveSchematicToFollowPlayer(currentPlayerPosFlat);
                lastPlayerPos = currentPlayerPosFlat;
            }
        } catch (Exception e) {
            if (logMovement.get()) {
                error("Failed to handle follow player: " + e.getMessage());
            }
        }
    }

    private void moveSchematicToFollowPlayer(BlockPos playerPos) {
        SchematicPlacement placement = getCurrentPlacement();
        if (placement == null || relativeOffset == null) {
            return;
        }

        try {
            BlockPos currentSchematicPos = placement.getOrigin();

            // Calculate new schematic position based on player position and relative offset
            BlockPos newSchematicPos = new BlockPos(
                playerPos.getX() + relativeOffset.getX(),
                currentSchematicPos.getY(), // Keep original Y position
                playerPos.getZ() + relativeOffset.getZ()
            );

            // Only move if position actually changed
            if (!currentSchematicPos.equals(newSchematicPos)) {
                placement.setOrigin(newSchematicPos, null);

                if (logMovement.get()) {
                    info("Followed player: moved schematic to " + newSchematicPos.toShortString());
                }
            }
        } catch (Exception e) {
            if (logMovement.get()) {
                error("Failed to move schematic to follow player: " + e.getMessage());
            }
        }
    }

    // Utility method to reset follow player
    public void resetFollowPlayer() {
        isFollowingInitialized = false;
        lastPlayerPos = null;
        relativeOffset = null;
        if (logMovement.get()) {
            info("Follow player reset");
        }
    }

    // Getters for follow player state
    public boolean isFollowPlayerEnabled() {
        return followPlayer.get();
    }

    public boolean isFollowPlayerInitialized() {
        return isFollowingInitialized;
    }

    public BlockPos getRelativeOffset() {
        return relativeOffset;
    }
}
